#ok
locals{
  account_id                         = "************"
  first_run                            = false
  main_container_name                  = "BillPaymentAPI" #ok
  bill_payment_scheduling_sqs_name     = "bill_payment_scheduling" #ok
  bank_account_invalidation_sqs_name   = "bank-account-invalidation" #ok
  bill_events_sns_name                 = "bill-events" #ok
  wallet_events_sns_name               = "wallet-events" #ok
  account_events_sns_name              = "account-events" #ok
  # ecs_sqs_policy_resource              = ["arn:aws:sqs:${var.aws_region}:${var.account_id}:*"] #TODO: remover,  não usado
  ecs_sqs_list_policy_resource         = ["arn:aws:sqs:us-east-1:${var.account_id}:*"] #ok
  bill_payment_service_task_definition         = "${var.task_definitions_base_path}/bill-payment.json"
  settlement_service_task_definition         = "${var.task_definitions_base_path}/settlement-service.json"
  dda_service_task_definition         = "${var.task_definitions_base_path}/dda-service.json"
  ai-chatbot_service_task_definition    = "${var.task_definitions_base_path}/ai-chatbot.json"
  investment-manager_service_task_definition    = "${var.task_definitions_base_path}/investment-manager-service.json"
  open-finance_service_task_definition         = "${var.task_definitions_base_path}/open-finance.json"
  has_dda_bills = true
  has_chatbot = var.environment == "production"
}

module "bill-payment-buckets" {
  source                    = "../bill-payment-buckets"
  create_public_bucket      = var.bill_payment_create_public_bucket
  user_receipts_bucket_name = var.user_receipts_bucket_name
  public_bucket_name        = "me-poupe-contas-bill-payment-public-${var.environment}"
  user_receipts_object_expiration = var.user_receipts_object_expiration
}

module "html_to_image" {
  source = "./modules/html-to-image-lambda"
  html_to_image_bucket_name = "me-poupe-bill-payment-html-to-image-bucket-${var.environment}"
  lambda_name = "mepoupe-html-to-image"
  lambda_handler = "index.handler"
  lambda_iam_role_name = "html-to-image-role-name"
  aws_vpc_id = var.aws_vpc_id
  aws_subnet_ids = var.aws_public_subnet_id
  security_groups = [
    module.bill_payment_service.ecs_tasks_security_group_id,
  ]
  environment = var.environment
}

module "bill_payment_task" {
  source = "../fargate_task"
  first_run = local.first_run
  prefix                = "${var.prefix}-bill-payment"
  service_name =  "${var.prefix}-bill-payment-service"
  ecr_repository_name   = "bill-payment-api"
  fargate_cpu           = var.environment == "production" ? 4096 : 256
  fargate_memory        = var.environment == "production" ? 8192 : 2048
  other_container_definitions = {
    cpu = var.environment == "production" ? 4045 : 246
    memory = var.environment == "production" ? 7936 : 1792
  }
  task_definition       = local.bill_payment_service_task_definition
  dynamo_access_enabled = true
  ecs_dynamo_policy_resource = [
    aws_dynamodb_table.user_table.arn,
    "${aws_dynamodb_table.user_table.arn}/*",
    aws_dynamodb_table.event_table.arn,
    "${aws_dynamodb_table.event_table.arn}/*",
    aws_dynamodb_table.server_lock_table.arn,
    "${aws_dynamodb_table.server_lock_table.arn}/*",
    aws_dynamodb_table.user_event_table.arn,
    "${aws_dynamodb_table.user_event_table.arn}/*",
    module.open_finance.open_finance_data_table_arn,
    "${module.open_finance.open_finance_data_table_arn}/*",
  ]
  lambda_access_enabled = true
  ecs_lambda_policy_resource = [
    module.html_to_image.aws_lambda_function_arn,
  ]
  sqs_access_enabled      = true
  ecs_sqs_policy_resource = local.ecs_sqs_list_policy_resource

  sns_access_enabled      = true
  ecs_sns_policy_resource = concat([
    module.bill_events_sns.topic_arn,
    module.wallet_events_sns.topic_arn,
    module.account_events_sns.topic_arn,
  ],
    var.sns_topics)
  s3_read_objects = true
  s3_bucket_arns = [
    module.ses-email-receiver.incoming_emails_bucket_arn,
    module.ses-email-receiver.email_receiver_s3_bucket_arn,
    module.ses-email-receiver.unprocessed_emails_bucket_arn,
    module.ses-email-receiver.quarantine_emails_bucket_arn,
    aws_s3_bucket.bill_payment_documents_bucket.arn,
    aws_s3_bucket.multicom_files_bucket.arn,
    module.bill-payment-buckets.user_receipts_bucket_arn,
    var.connect_utility_errors_bucket_arn,
  ]
  textract_enabled = true
  secrets_enabled  = true
  secrets_arns     = [
    var.datadog_key_arn,
    aws_secretsmanager_secret.blip_password.arn,
    aws_secretsmanager_secret.bigdatacorp_credentials.arn,
    aws_secretsmanager_secret.friday_userpilot.arn,
    aws_secretsmanager_secret.via1_auth-secrets.arn,
    aws_secretsmanager_secret.via1_celcoin-credentials.arn,//5
    aws_secretsmanager_secret.via1_arbi-app-credentials.arn,
    aws_secretsmanager_secret.via1_arbi-ecm-credentials.arn,
    aws_secretsmanager_secret.friday_intercom-credentials.arn //8
  ]
  send_email    = true
  user_pool_arn = module.via1_cognito.user_pool_arn
  kms_enabled   = true
  kms_key_arns  = [aws_kms_key.connect_utility_key.arn]


  cluster_name  = var.aws_ecs_bill_payment_cluster.name
  container_name = local.main_container_name
  app_environments = var.app_environments
  environment = var.environment
  fluent_bit_repository_url = var.fluent_bit_repository_url
  account_id = var.account_id
  app_port              = 8443
  tags = var.tags
  user_pool_arn_enabled  = true
  ephemeral_storage = 21
}

module "bill_payment_service" {
  source = "../fargate_service"

  aws_ecs_cluster       = var.aws_ecs_bill_payment_cluster
  aws_private_subnet_id = var.aws_private_subnet_id
  aws_public_subnet_id  = var.aws_public_subnet_id
  aws_vpc_id            = var.aws_vpc_id
  prefix                = "${var.prefix}-bill-payment"
  container_name        = local.main_container_name
  app_port              = 8443
  app_count             = 1
  app_protocol          = "HTTPS"
  health_check_path     = "/health"
  task_definition       = module.bill_payment_task
  certificate_arn       = var.certificate_arn
  load_balance_enabled  = true
}

resource "aws_security_group" "bill-payment-cache-cluster" {
  name        = "${var.prefix}-bill-payment-cache-cluster-sg"
  description = "${var.prefix}-bill-payment-cache-cluster"
  vpc_id      = var.aws_vpc_id

  ingress {
    protocol        = "6"
    from_port       = "6379"
    to_port         = "6379"
    security_groups = [
      module.bill_payment_service.ecs_tasks_security_group_id,
      module.ecs_dda_bills[0].ecs_tasks_security_group_id,
      module.ecs_investment-manager.ecs_tasks_security_group_id
    ]
  }
}

resource "aws_s3_bucket" "bill_payment_documents_bucket" {
  bucket = "${var.account_id}-${var.prefix}-bill-payment-user-documents"

  # Prevent accidental deletion of this S3 bucket
  lifecycle {
    prevent_destroy = true
  }

  versioning {
    enabled = true
  }

  # Enable server-side encryption by default
  server_side_encryption_configuration {
    rule {
      apply_server_side_encryption_by_default {
        sse_algorithm = "AES256"
      }
    }
  }

  tags = {
    Environment = var.environment
  }
}

resource "aws_s3_bucket" "multicom_files_bucket" {
  bucket = "${var.account_id}-${var.prefix}-multicom-files"

  # Prevent accidental deletion of this S3 bucket
  lifecycle {
    prevent_destroy = true
  }

  versioning {
    enabled = false
  }

  # Enable server-side encryption by default
  server_side_encryption_configuration {
    rule {
      apply_server_side_encryption_by_default {
        sse_algorithm = "AES256"
      }
    }
  }

  tags = {
    Environment = var.environment
  }
}

resource "aws_dynamodb_table" "user_table" {
  name         = "Via1-BillPayment"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "PrimaryKey"
  range_key    = "ScanKey"

  ttl {
    attribute_name = "ExpirationTTL"
    enabled        = true
  }

  attribute {
    name = "PrimaryKey"
    type = "S"
  }

  attribute {
    name = "ScanKey"
    type = "S"
  }

  attribute {
    name = "GSIndex1PrimaryKey"
    type = "S"
  }

  attribute {
    name = "GSIndex1ScanKey"
    type = "S"
  }

  attribute {
    name = "GSIndex2PrimaryKey"
    type = "S"
  }

  attribute {
    name = "GSIndex2ScanKey"
    type = "S"
  }

  attribute {
    name = "GSIndex3PrimaryKey"
    type = "S"
  }

  attribute {
    name = "GSIndex3ScanKey"
    type = "S"
  }

  attribute {
    name = "GSIndex4PrimaryKey"
    type = "S"
  }

  attribute {
    name = "GSIndex4ScanKey"
    type = "S"
  }

  global_secondary_index {
    name            = "GSIndex1"
    hash_key        = "GSIndex1PrimaryKey"
    range_key       = "GSIndex1ScanKey"
    projection_type = "ALL"
    read_capacity   = 0
    write_capacity  = 0
  }

  global_secondary_index {
    name            = "GSIndex2"
    hash_key        = "GSIndex2PrimaryKey"
    range_key       = "GSIndex2ScanKey"
    projection_type = "ALL"
    read_capacity   = 0
    write_capacity  = 0
  }

  global_secondary_index {
    name            = "GSIndex3"
    hash_key        = "GSIndex3PrimaryKey"
    range_key       = "GSIndex3ScanKey"
    projection_type = "ALL"
    read_capacity   = 0
    write_capacity  = 0
  }

  global_secondary_index {
    name            = "GSIndex4"
    hash_key        = "GSIndex4PrimaryKey"
    range_key       = "GSIndex4ScanKey"
    projection_type = "ALL"
    read_capacity   = 0
    write_capacity  = 0
  }

  tags = {
    Environment = var.environment
  }
}

resource "aws_dynamodb_table" "event_table" {
  name         = "Via1-BillEvents"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "PrimaryKey"
  range_key    = "ScanKey"

  attribute {
    name = "PrimaryKey"
    type = "S"
  }

  attribute {
    name = "ScanKey"
    type = "N"
  }

  attribute {
    name = "GSIndex1PrimaryKey"
    type = "S"
  }

  attribute {
    name = "GSIndex1ScanKey"
    type = "S"
  }

  global_secondary_index {
    name            = "GSIndex1"
    hash_key        = "GSIndex1PrimaryKey"
    range_key       = "GSIndex1ScanKey"
    projection_type = "ALL"
  }

  tags = {
    Environment = var.environment
  }
}

resource "aws_dynamodb_table" "server_lock_table" {
  name         = "Shedlock"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "_id"

  attribute {
    name = "_id"
    type = "S"
  }

  tags = {
    Environment = var.environment
  }
}

resource "aws_backup_plan" "dynamodb_backup_plan" {
  name = "dynamodb-backup-plan"
  count = var.environment == "production" ? 1 : 0

  rule {
    rule_name         = "DynamoDBHourlySnapshotsRule"
    target_vault_name = "Default"
    schedule          = "cron(0 6/1 ? * * *)"
    start_window      = 60
    completion_window = 120

    lifecycle {
      delete_after = 3  # Retain backups for 3 days
    }
  }

  rule {
    rule_name         = "DynamoDBDailyBackupRule"
    target_vault_name = "Default"
    schedule          = "cron(10 6 ? * * *)"
    start_window      = 480
    completion_window = 1008

    lifecycle {
      delete_after = 8  # Retain backups for 35 days
    }
  }

  rule {
    rule_name         = "DynamoDBWeeklyBackupRule"
    target_vault_name = "Default"
    schedule          = "cron(20 6 ? * 7 *)"
    start_window      = 480
    completion_window = 10080

    lifecycle {
      delete_after = 35  # Retain backups for 35 days
    }
  }

  rule {
    rule_name         = "DynamoDBMonthlyBackupRule"
    target_vault_name = "Default"
    schedule          = "cron(30 6 1 * ? *)"
    start_window      = 480
    completion_window = 1440

    lifecycle {
      delete_after = 390  # Retain backups for 390 days
    }
  }

  rule {
    rule_name         = "DynamoDBYearlyBackupRule"
    target_vault_name = "Default"
    schedule          = "cron(40 6 2 1 ? *)"
    start_window      = 480
    completion_window = 2880

    lifecycle {
      delete_after = 2190  # Retain backups for 2190 days
    }
  }
}

resource "aws_backup_selection" "dynamodb_backup_selection" {
  name          = "dybanodb-backup-selection"
  count = var.environment == "production" ? 1 : 0
  iam_role_arn  = aws_iam_role.dynamodb_backup[0].arn
  plan_id       = aws_backup_plan.dynamodb_backup_plan[0].id

  resources = [
    aws_dynamodb_table.user_table.arn,
    aws_dynamodb_table.event_table.arn
  ]

  depends_on = [aws_backup_plan.dynamodb_backup_plan]
}

resource "aws_iam_role" "dynamodb_backup" {
  name = "dynamodb-backup"
  count = var.environment == "production" ? 1 : 0

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action = "sts:AssumeRole",
        Effect = "Allow",
        Principal = {
          Service = "backup.amazonaws.com"
        }
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "dynamodb_backup" {
  count = var.environment == "production" ? 1 : 0
  role       = aws_iam_role.dynamodb_backup[0].name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSBackupServiceRolePolicyForBackup"
  depends_on = [aws_iam_role.dynamodb_backup[0]]
}

resource "aws_elasticache_subnet_group" "bill-payment-cache-cluster" {
  name       = "${var.prefix}-bill-payment-cache-subnet"
  subnet_ids = var.aws_private_subnet_id
}

resource "aws_elasticache_cluster" "bill-payment-cache-cluster" {
  cluster_id           = "${var.prefix}-bill-payment-cache"
  engine               = "redis"
  node_type            = "cache.t2.micro"
  num_cache_nodes      = 1
  parameter_group_name = "default.redis7"
  port                 = 6379
  engine_version       = "7.0"
  subnet_group_name    = aws_elasticache_subnet_group.bill-payment-cache-cluster.name
  security_group_ids   = [aws_security_group.bill-payment-cache-cluster.id]

}

module "bill_events_dlq" {
  source  = "terraform-aws-modules/sqs/aws"
  version = "~> 2.0"
  name    = "bill_events_dlq"
}

module "bill_payment_rollback_transaction_queue" {
  source                     = "terraform-aws-modules/sqs/aws"
  version                    = "~> 2.0"
  name                       = "bill_payment_rollback_transaction"
  delay_seconds              = 30
  receive_wait_time_seconds  = 20
  visibility_timeout_seconds = 30
  redrive_policy             = "{\"deadLetterTargetArn\":\"${module.bill_payment_rollback_transaction_dlq.this_sqs_queue_arn}\",\"maxReceiveCount\":144}"

  tags = {
    Environment = var.environment
  }
}

module "bill_payment_rollback_transaction_dlq" {
  source  = "terraform-aws-modules/sqs/aws"
  version = "~> 2.0"
  name    = "bill_payment_rollback_transaction_dlq"

  tags = {
    Environment = var.environment
  }
}

module "bill_notification" {
  source  = "terraform-aws-modules/sqs/aws"
  version = "~> 2.0"
  name    = "bill_notification"

  receive_wait_time_seconds  = 20
  visibility_timeout_seconds = 300
  redrive_policy             = "{\"deadLetterTargetArn\":\"${module.bill_notification_dlq.this_sqs_queue_arn}\",\"maxReceiveCount\":144}"

  tags = {
    Environment = var.environment
  }
}

module "bill_notification_dlq" {
  source  = "terraform-aws-modules/sqs/aws"
  version = "~> 2.0"
  name    = "bill_notification_dlq"

  tags = {
    Environment = var.environment
  }
}



#ok
module "ses-email-receiver" {
  source                                                 = "../ses" #ok
  ses_receiver_rule_name                                 = var.ses_receiver_rule_name #ok
  email_domain                                           = var.email_domain #ok
  scan_enabled                                           = true #ok
  ses_bucket_name                                        = var.ses_bucket_name #ok

  ses_unprocessed_emails_bucket_name                     = var.ses_unprocessed_emails_bucket_name #ok
  ses_unprocessed_emails_bucket_replication_enabled      = var.ses_unprocessed_emails_bucket_replication_enabled #ok    
  ses_unprocessed_emails_bucket_replication_rule_id      = var.ses_unprocessed_emails_bucket_replication_rule_id #ok    
  ses_unprocessed_emails_bucket_replication_destination  = var.ses_unprocessed_emails_bucket_replication_destination #ok
  ses_unprocessed_emails_bucket_replication_role         = var.ses_unprocessed_emails_bucket_replication_role #ok       
  region                                                 = var.aws_region #ok

  ses_incoming_emails_bucket_name                        = var.ses_incoming_emails_bucket_name #ok
  ses_incoming_emails_bucket_replication_enabled         = var.ses_incoming_emails_bucket_replication_enabled #ok
  ses_incoming_emails_bucket_replication_rule_id         = var.ses_incoming_emails_bucket_replication_rule_id #ok
  ses_incoming_emails_bucket_replication_destination     = var.ses_incoming_emails_bucket_replication_destination #ok
  ses_incoming_emails_bucket_replication_role            = var.ses_incoming_emails_bucket_replication_role #ok

  rendering_errors_to_s3_bucket_enabled                  = var.rendering_errors_to_s3_bucket_enabled #ok
  rendering_bucket_name                                  = "email-template-rendering-errors-me-poupe-contas" #ok
  sns_receiver_emails_arn                                = module.incoming_emails.topic_arn #ok
  sns_failure_rendering_arn                              = module.failure_rendering_notification.topic_arn #ok
  quarantine_emails_bucket_name                          = var.quarantine_emails_bucket_name #ok
  notification_email_sender                              = var.notification_email_sender #ok
}

module "via1_cognito" {
  source                           = "../cognito"
  prefix                           = var.prefix
  user_pool_name                   = "${var.account_id}-${var.prefix}-user-pool"
  user_pool_domain_name            = var.environment == "production" ? "auth-me-poupe-contas.mepoupe.app" : "auth-me-poupe-contas.meupagador.com.br"
  user_pool_domain_certificate_arn = var.certificate_arn
  user_pool_domain_zone_id         = ""
  provider_google_app_id           = ""
  provider_google_app_secret       = ""
  notification_email_sender_arn    = module.ses-email-receiver.notification_email_sender_arn
  provider_apple_client_id         = ""
  provider_apple_team_id           = ""
  provider_apple_key_id            = ""
  provider_apple_private_key       = ""
  id_token_validity                = 60
  access_token_validity            = 60
  datadog_key_arn                  = var.datadog_key_arn
  callback_urls                    = var.environment == "production" ? [ "https://use-me-poupe-contas.mepoupe.app/autenticacao-federado"] : ["https://use.local.via1.io:20444/autenticacao-federado", "https://use-me-poupe-contas.meupagador.com.br/autenticacao-federado"]
  logout_urls                      = var.environment == "production" ? ["https://use-me-poupe-contas.mepoupe.app/"] : ["https://use.local.via1.io:20444/", "https://use-me-poupe-contas.meupagador.com.br/"]
  lambda_config_enabled            = true
  environment                       = var.environment
}

resource "aws_kms_key" "connect_utility_key" {
  description             = "KMS key for bill payment encryption"
  deletion_window_in_days = 10
  multi_region            = true
}

resource "aws_kms_alias" "connect_utility_key" {
  name          = "alias/utility-connect-key"
  target_key_id = aws_kms_key.connect_utility_key.key_id
}

provider "aws" {
  alias  = "replica"
  region = "sa-east-1"
}

resource "aws_kms_replica_key" "connect_utility_key_replica" {
  provider = aws.replica

  description             = "KMS key for bill payment encryption replica"
  deletion_window_in_days = 10
  primary_key_arn         = aws_kms_key.connect_utility_key.arn
}

resource "aws_kms_alias" "connect_utility_key_replica" {
  provider = aws.replica

  name          = "alias/utility-connect-key"
  target_key_id = aws_kms_replica_key.connect_utility_key_replica.key_id
}

data "aws_iam_policy_document" "received-emails" {
  statement {
    actions   = ["sqs:SendMessage"]
    resources = ["arn:aws:sqs:${var.aws_region}:${var.account_id}:${var.incoming_emails_sqs_name}"]
    principals {
      type        = "AWS"
      identifiers = ["*"]
    }
    condition {
      test     = "ArnEquals"
      values   = ["arn:aws:sns:${var.aws_region}:${var.account_id}:${var.incoming_emails_sns_name}"]
      variable = "aws:SourceArn"
    }
    effect = "Allow"
  }
}

module "incoming_emails_queue" {
  source         = "terraform-aws-modules/sqs/aws"
  version        = "~> 2.0"
  policy         = data.aws_iam_policy_document.received-emails.json
  name           = var.incoming_emails_sqs_name
  redrive_policy = "{\"deadLetterTargetArn\":\"${module.incoming_emails_dlq.this_sqs_queue_arn}\",\"maxReceiveCount\":1}"

  tags = {
    Environment = var.environment
  }
}

module "incoming_emails_dlq" {
  source  = "terraform-aws-modules/sqs/aws"
  version = "~> 2.0"
  name    = "${var.incoming_emails_sqs_name}-dlq"

  tags = {
    Environment = var.environment
  }
}

module "incoming_emails" {
  source = "terraform-aws-modules/sns/aws"
  name   = var.incoming_emails_sns_name
}

resource "aws_sns_topic_subscription" "incoming_emails" {
  topic_arn            = module.incoming_emails.topic_arn
  protocol             = "sqs"
  endpoint             = module.incoming_emails_queue.this_sqs_queue_arn
  raw_message_delivery = true
}

module "failure_rendering_notification" {
  source = "terraform-aws-modules/sns/aws"
  name   = "rendering-failed-emails"
}

resource "aws_secretsmanager_secret" "blip_password" {
  name = "bill-payment-api/blip-password"
}

resource "aws_secretsmanager_secret" "friday_userpilot" {
  name = "friday/userpilot"
}

resource "aws_secretsmanager_secret" "bigdatacorp_credentials" {
  name = "via1/bigdatacorp-credentials"
}

resource "aws_secretsmanager_secret" "via1_auth-secrets" {
  name = "via1/auth-secrets"
}

resource "aws_secretsmanager_secret" "via1_celcoin-credentials" {
  name = "via1/celcoin-credentials"
}

resource "aws_secretsmanager_secret" "via1_arbi-app-credentials" {
  name = "via1/arbi-app-credentials"
}

resource "aws_secretsmanager_secret" "via1_arbi-ecm-credentials" {
  name = "via1/arbi-ecm-credentials"
}

resource "aws_secretsmanager_secret" "friday_intercom-credentials" {
  name = "friday/intercom-credentials"
}

data "aws_iam_policy_document" "bank_account_policy" {
  statement {
    actions   = ["sqs:SendMessage"]
    resources = ["arn:aws:sqs:${var.aws_region}:${var.account_id}:${local.bank_account_invalidation_sqs_name}"]
    principals {
      type        = "AWS"
      identifiers = ["*"]
    }
    condition {
      test     = "ArnEquals"
      values   = [module.bill_events_sns.topic_arn]
      variable = "aws:SourceArn"
    }
    effect = "Allow"
  }
}

data "aws_iam_policy_document" "bill_payment_scheduling_queue_policy" {
  statement {
    actions   = ["sqs:SendMessage"]
    resources = ["arn:aws:sqs:${var.aws_region}:${var.account_id}:${local.bill_payment_scheduling_sqs_name}"]
    principals {
      type        = "AWS"
      identifiers = ["*"]
    }
    condition {
      test     = "ArnEquals"
      values   = [module.bill_events_sns.topic_arn]
      variable = "aws:SourceArn"
    }
    effect = "Allow"
  }
}

resource "aws_sns_topic_subscription" "bank_account_invalidation_queue_subscription" {
  topic_arn            = module.bill_events_sns.topic_arn
  protocol             = "sqs"
  endpoint             = module.bank_account_invalidation_queue.this_sqs_queue_arn
  filter_policy        = jsonencode({ "eventType" = tolist(["PAYMENT_FAIL", "PAYMENT_REFUNDED"]) })
  raw_message_delivery = true
}

resource "aws_sns_topic_subscription" "bill_payment_scheduling_queue_subscription" {
  topic_arn     = module.bill_events_sns.topic_arn
  protocol      = "sqs"
  endpoint      = module.bill_payment_scheduling_queue.this_sqs_queue_arn
  filter_policy = jsonencode({
    "eventType" = tolist(["AMOUNT_UPDATED", "PAYMENT_SCHEDULED", "PAYMENT_SCHEDULE_CANCELED", "REGISTER_UPDATED"])
  })
  raw_message_delivery = true
}

module "bill_events_sns" {
  source = "terraform-aws-modules/sns/aws"
  name   = local.bill_events_sns_name
}

module "wallet_events_sns" {
  source = "terraform-aws-modules/sns/aws"
  name   = local.wallet_events_sns_name
}

module "account_events_sns" {
  source = "terraform-aws-modules/sns/aws"
  name   = local.account_events_sns_name
}

module "bank_account_invalidation_queue" {
  source                     = "terraform-aws-modules/sqs/aws"
  version                    = "~> 2.0"
  policy                     = data.aws_iam_policy_document.bank_account_policy.json
  name                       = local.bank_account_invalidation_sqs_name
  receive_wait_time_seconds  = 20
  visibility_timeout_seconds = 300
  redrive_policy             = "{\"deadLetterTargetArn\":\"${module.bank_account_invalidation_dlq.this_sqs_queue_arn}\",\"maxReceiveCount\":60}"

  tags = {
    Environment = var.environment
  }
}

module "bank_account_invalidation_dlq" {
  source  = "terraform-aws-modules/sqs/aws"
  version = "~> 2.0"
  name    = "bank-account-invalidation-dlq"

  tags = {
    Environment = var.environment
  }
}

module "bill_payment_scheduling_queue" {
  source                     = "terraform-aws-modules/sqs/aws"
  version                    = "~> 2.0"
  policy                     = data.aws_iam_policy_document.bill_payment_scheduling_queue_policy.json
  name                       = local.bill_payment_scheduling_sqs_name
  receive_wait_time_seconds  = 20
  visibility_timeout_seconds = 300
  redrive_policy             = "{\"deadLetterTargetArn\":\"${module.bill_payment_scheduling_dlq.this_sqs_queue_arn}\",\"maxReceiveCount\":60}"

  tags = {
    Environment = var.environment
  }
}

module "bill_payment_scheduling_dlq" {
  source  = "terraform-aws-modules/sqs/aws"
  version = "~> 2.0"
  name    = "${local.bill_payment_scheduling_sqs_name}_dlq"

  tags = {
    Environment = var.environment
  }
}

module "utility_account_dlq" {
  source  = "terraform-aws-modules/sqs/aws"
  version = "~> 2.0"
  name    = "utility_account_dlq"
}

module "ecs_settlement-service" {
  source = "../settlement-service"
  aws_ecs_cluster              = var.aws_ecs_bill_payment_cluster
  environment                  = var.environment
  app_environments             = var.app_environments
  aws_private_subnet_id        = var.aws_private_subnet_id
  aws_public_subnet_id         = var.aws_public_subnet_id
  aws_vpc_id                   = var.aws_vpc_id
  prefix                       = var.prefix
  task_definition            = local.settlement_service_task_definition
  certificate_arn              = var.certificate_arn
  dynamo_access_enabled        = true
  s3_read_objects              = false
  s3_bucket_arns               = []
  secrets_arns                 = [
    var.datadog_key_arn,
    aws_secretsmanager_secret.via1_auth-secrets.arn,
    aws_secretsmanager_secret.via1_arbi-app-credentials.arn,
    aws_secretsmanager_secret.via1_celcoin-credentials.arn,
  ]
  fluent_bit_repository_url  = var.fluent_bit_repository_url
  sqs_access_enabled         = true
  ecs_sqs_policy_resource    = local.ecs_sqs_list_policy_resource
  account_id                 = var.account_id
  tags                       = {
    Project = "settlement-service"
  }
}

module "ecs_dda_bills" {
  source = "../dda-bills-service"
  count = local.has_dda_bills ? 1:0
  aws_ecs_cluster              = var.aws_ecs_bill_payment_cluster
  environment                  = var.environment
  app_environments             = var.app_environments
  aws_private_subnet_id        = var.aws_private_subnet_id
  aws_public_subnet_id         = var.aws_public_subnet_id
  aws_vpc_id                   = var.aws_vpc_id
  prefix                       = var.prefix
  task_definition            = local.dda_service_task_definition
  certificate_arn              = var.certificate_arn
  dynamo_access_enabled        = true
  secrets_arns = [
    var.datadog_key_arn,
    aws_secretsmanager_secret.via1_auth-secrets.arn,
  ]
  fluent_bit_repository_url  = var.fluent_bit_repository_url
  sqs_access_enabled         = true
  ecs_sqs_policy_resource    = local.ecs_sqs_list_policy_resource
  account_id                 = var.account_id
  ecs_dynamo_policy_resource = [
    aws_dynamodb_table.user_table.arn,
    "${aws_dynamodb_table.user_table.arn}/*",
    aws_dynamodb_table.server_lock_table.arn,
    "${aws_dynamodb_table.server_lock_table.arn}/*"
  ]
  tags                       = {
    Project = "dda-bills-service"
  }
}


module "ecs_ai-chatbot-service" {
  count = local.has_chatbot ? 1:0
  source = "../ai-chatbot-service"
  aws_ecs_cluster              = var.aws_ecs_bill_payment_cluster
  environment                  = var.environment
  app_environments             = var.app_environments
  aws_private_subnet_id        = var.aws_private_subnet_id
  aws_public_subnet_id         = var.aws_public_subnet_id
  aws_vpc_id                   = var.aws_vpc_id
  prefix                       = var.prefix
  task_definition            = local.ai-chatbot_service_task_definition
  certificate_arn              = var.certificate_arn
  dynamo_access_enabled        = true
  ecs_dynamo_policy_resource   = [
    aws_dynamodb_table.user_table.arn,
    "${aws_dynamodb_table.user_table.arn}/*",
    aws_dynamodb_table.server_lock_table.arn,
    "${aws_dynamodb_table.server_lock_table.arn}/*"]
  s3_read_objects              = false
  s3_bucket_arns               = []
  secrets_arns                 = [
    var.datadog_key_arn,
    aws_secretsmanager_secret.blip_password.arn,
  ]
  fluent_bit_repository_url  = var.fluent_bit_repository_url
  sqs_access_enabled         = true
  ecs_sqs_policy_resource    = local.ecs_sqs_list_policy_resource
  account_id                 = var.account_id
  cognito_token_sns_topic_arn = module.via1_cognito.cognito_token_sns_topic_arn
  cognito_custom_sender_kms_key_arn = module.via1_cognito.cognito_custom_sender_kms_key_arn
  tags                       = {
    Project = "ai-chatbot-service"
  }
}

module "ecs_investment-manager" {
  source = "../investment-manager-service"
  aws_ecs_cluster              = var.aws_ecs_bill_payment_cluster
  environment                  = var.environment
  app_environments             = var.app_environments
  aws_private_subnet_id        = var.aws_private_subnet_id
  aws_public_subnet_id         = var.aws_public_subnet_id
  aws_vpc_id                   = var.aws_vpc_id
  prefix                       = var.prefix
  task_definition            = local.investment-manager_service_task_definition
  certificate_arn              = var.certificate_arn
  dynamo_access_enabled        = true
  s3_read_objects              = false
  s3_bucket_arns               = []
  secrets_arns                 = [
    var.datadog_key_arn,
  ]
  fluent_bit_repository_url  = var.fluent_bit_repository_url
  sqs_access_enabled         = true
  ecs_sqs_policy_resource    = local.ecs_sqs_list_policy_resource
  account_id                 = var.account_id
  external_redis_endpoint    = "redis://${aws_elasticache_cluster.bill-payment-cache-cluster.cache_nodes.0.address}:${aws_elasticache_cluster.bill-payment-cache-cluster.cache_nodes.0.port}"
  tags                       = {
    Project = "investment-manager-service"
  }
}


resource "aws_dynamodb_table" "user_event_table" {
  name         = "Via1-UserEvents"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "PrimaryKey"
  range_key    = "ScanKey"

  point_in_time_recovery {
    enabled = true
  }

  attribute {
    name = "PrimaryKey"
    type = "S"
  }

  attribute {
    name = "ScanKey"
    type = "S"
  }

  attribute {
    name = "GSIndex1PrimaryKey"
    type = "S"
  }

  attribute {
    name = "GSIndex1ScanKey"
    type = "S"
  }

  attribute {
    name = "GSIndex2PrimaryKey"
    type = "S"
  }

  attribute {
    name = "GSIndex2ScanKey"
    type = "S"
  }

  global_secondary_index {
    name            = "GSIndex1"
    hash_key        = "GSIndex1PrimaryKey"
    range_key       = "GSIndex1ScanKey"
    projection_type = "ALL"
  }

  global_secondary_index {
    name            = "GSIndex2"
    hash_key        = "GSIndex2PrimaryKey"
    range_key       = "GSIndex2ScanKey"
    projection_type = "ALL"
  }

  tags = {
    Environment = var.environment
  }
}

module "open_finance" {
  source = "../../modules/open-finance"
  first_run = true
  aws_region                = "us-east-1"
  environment               = var.environment
  task_definition           = local.open-finance_service_task_definition
  fluent_bit_repository_url = var.fluent_bit_repository_url
  private_subnets           = var.aws_private_subnet_id
  public_subnets            = var.aws_public_subnet_id
  vpc_id                    = var.aws_vpc_id
  certificate_arn           = var.certificate_arn
  account_id                = local.account_id
  fargate_cpu               = 1024
  fargate_memory            = 4096
  dynamo_access_enabled     = true
  sqs_access_enabled        = true
  ecs_sqs_policy_resource   = ["arn:aws:sqs:${var.aws_region}:${var.account_id}:*"]
  app_environments          = var.app_environments
  prefix                    = var.prefix
  aws_ecs_cluster           = var.aws_ecs_bill_payment_cluster
  secrets_arns              = [
    var.datadog_key_arn,
  ]
  tags                       = {
    Project = "open-finance-service"
  }
}