locals { 
  account_id                         = "************"
  production_availability_zones      = ["us-east-1a", "us-east-1b"]
  tenant                               = "me-poupe"
  public_subnets  = ["10.0.1.0/24", "10.0.2.0/24"]
  private_subnets = ["10.0.101.0/24", "10.0.102.0/24"]
  database_subnets = ["10.0.151.0/24", "10.0.152.0/24"]
  ecs_sqs_list_policy_resource         = ["arn:aws:sqs:us-east-1:${local.account_id}:*"]
  function_name                        = "mepoupe-ios-notification-webhook"
  ios_store_notification_subet = "17.0.0.0/8"
}

terraform {
  backend "s3" {
    bucket         = "me-poupe-backend-infrastructure-production"
    key            = "backend/terraform.tfstate"
    dynamodb_table = "backend-infrastructure-lock-table"
    encrypt        = true
    region         = "us-east-1"
  }
}

provider "aws" {
  region              = var.aws_region
  allowed_account_ids = [local.account_id]
  version            = "~> 5.0"
}


module "backend" {
  source              = "../../modules/backend"
  environment         = var.environment
  backend_bucket_name = "${local.account_id}-me-poupe-backend-infrastructure-prod"
  dynamodb_table_name = "backend-infrastructure-lock-table"
}

module "bill-payment-service" {
  source = "../../modules/bill-payment-service"
  task_definitions_base_path = "./task-definitions"
  environment = var.environment
  aws_region = var.aws_region
  account_id = local.account_id

  aws_private_subnet_id = module.vpc.private_subnets
  aws_public_subnet_id  = module.vpc.public_subnets
  aws_vpc_id            = module.vpc.vpc_id

  //aws_ecs_me_poupe_cluster = aws_ecs_cluster.me-poupe_fargate_cluster
  aws_ecs_bill_payment_cluster = aws_ecs_cluster.bill_payment_fargate_cluster

  prefix = local.tenant
  certificate_arn = aws_acm_certificate.mepoupeapp.arn

  datadog_key_arn = aws_secretsmanager_secret.datadog_key.arn

  fluent_bit_repository_url = aws_ecr_repository.fluent_bit_ecr.repository_url

  app_environments="${local.tenant},prod${local.tenant}"

  sns_topics = [] //module.me-poupe-api.me_poupe_user_updated_events_sns_topic_arn

  tags = {
    Environment = var.environment
  }

  #New params
  incoming_emails_sns_name = "incoming-emails"
  incoming_emails_sqs_name = "incoming-emails"
  bill_payment_create_public_bucket = true
  connect_utility_errors_bucket_arn = "arn:aws:s3:::mepoupe-connect-utility-errors"
  user_receipts_bucket_name =  "me-poupe-contas-bill-receipts"
  user_receipts_object_expiration = 7

  ses_receiver_rule_name = "ses_receiver"
  email_domain = ["mailbox.mepoupe.app"]
  ses_bucket_name = "me-poupe-contas-received-emails-via1"
  rendering_errors_to_s3_bucket_enabled = false
  quarantine_emails_bucket_name = "quarantine-emails-me-poupe-contas"
  notification_email_sender = "<EMAIL>"

  ses_unprocessed_emails_bucket_name = "ses-unprocessed-emails-me-poupe-contas"
  ses_unprocessed_emails_bucket_replication_enabled = false #TODO: habilitar quando tivermos recebimento de email. Terraformar o replication bucket.
  ses_unprocessed_emails_bucket_replication_rule_id = "ReplicationToAnotherAccountRule"
  ses_unprocessed_emails_bucket_replication_destination = "arn:aws:s3:::replica-ses-unprocessed-emails-me-poupe-contas"
  ses_unprocessed_emails_bucket_replication_role = "arn:aws:iam::************:role/service-role/s3crr_role_for_ses-unprocessed-emails-via1"

  ses_incoming_emails_bucket_name = "me-poupe-contas-incoming-emails"
  ses_incoming_emails_bucket_replication_enabled = false #TODO: habilitar quando tivermos recebimento de email. Terraformar o replication bucket.
  ses_incoming_emails_bucket_replication_rule_id = "ReplicationToReplicaAccount"
  ses_incoming_emails_bucket_replication_destination = "arn:aws:s3:::replica-mepoupe-incoming-emails"
  ses_incoming_emails_bucket_replication_role = "arn:aws:iam::************:role/service-role/s3crr_role_for_via1-incoming-emails"
}

module "me-poupe-api" {
  source = "../../modules/me-poupe-api"
  environment = var.environment
  aws_region = var.aws_region
  account_id = local.account_id

  aws_private_subnet_id = module.vpc.private_subnets
  aws_public_subnet_id  = module.vpc.public_subnets
  aws_vpc_id            = module.vpc.vpc_id

  aws_ecs_cluster = aws_ecs_cluster.bill_payment_fargate_cluster //aws_ecs_cluster.me-poupe_fargate_cluster
  prefix = local.tenant
  certificate_arn = aws_acm_certificate.mepoupe.arn

  datadog_key_arn = aws_secretsmanager_secret.datadog_key.arn

  fluent_bit_repository_url = aws_ecr_repository.fluent_bit_ecr.repository_url
  app_environments = "prod"

  mp_environment_domain = var.mp_environment_domain

  tags = {
    Environment = var.environment
  }
}

/*module "keycloak" {
  source = "../../modules/keycloak"
  environment = var.environment
  aws_region = var.aws_region
  account_id = local.account_id

  aws_private_subnet_id = module.vpc.private_subnets
  aws_public_subnet_id  = module.vpc.public_subnets
  aws_vpc_id            = module.vpc.vpc_id

  aws_ecs_cluster = aws_ecs_cluster.bill_payment_fargate_cluster // aws_ecs_cluster.me-poupe_fargate_cluster
  prefix = local.tenant
  certificate_arn = aws_acm_certificate.mepoupe.arn

  datadog_key_arn = aws_secretsmanager_secret.datadog_key.arn

  fluent_bit_repository_url = aws_ecr_repository.fluent_bit_ecr.repository_url
  app_environments = ""

  database_subnet_group_name = module.vpc.database_subnet_group_name
  database_cidr_blocks = concat(local.private_subnets, local.public_subnets, local.database_subnets)

  tags = {
    Environment = var.environment
  }
  asg_min_size = 2
}*/

//ok
module "sns" {
  source              = "../../modules/sns"
  monthly_spend_limit = 600
}

module "ci" {
  source           = "../../modules/ci"
  prefix = local.tenant
  environment      = var.environment
  account_id       = local.account_id
  cf_distributions = ["E2R01Y35S0PEJ7"]
  aws_region       = var.aws_region
}

module "playstore-subscription" {
  source                  = "../../modules/playstore-subscription"
  prefix                  = local.tenant
  environment             = var.environment
  account_id              = local.account_id
  aws_region            = var.aws_region
}

module "vpc" {
  source  = "terraform-aws-modules/vpc/aws"
  version = "5.1.2"
  name    = "${local.tenant}-vpc"
  cidr    = "10.0.0.0/16"

  azs             = ["us-east-1a", "us-east-1b"]
  public_subnets  = local.public_subnets
  private_subnets = local.private_subnets

  enable_nat_gateway = var.nat_gateway_enabled
  single_nat_gateway = true
  enable_vpn_gateway = false
  enable_dns_hostnames    = false
  map_public_ip_on_launch = true

  database_subnet_group_name = "${local.tenant}-db-subnet-group"
  create_database_subnet_group = true
  create_database_subnet_route_table = false
  database_subnets           = local.database_subnets

  tags = {
    Terraform   = "true"
    Environment = var.environment
  }
}

resource "aws_acm_certificate" "default" {
  domain_name               = "friday.ai"
  subject_alternative_names = ["*.friday.ai"]
  validation_method         = "DNS"
  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_acm_certificate" "mepoupe" {
  domain_name               = "mepoupe.com"
  subject_alternative_names = ["*.mepoupe.com"]
  validation_method         = "DNS"
  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_acm_certificate" "mepoupeapp" {
  domain_name               = "mepoupe.app"
  subject_alternative_names = ["*.mepoupe.app"]
  validation_method         = "DNS"
  lifecycle {
    create_before_destroy = true
  }
}
/*
resource "aws_ecs_cluster" "me-poupe_fargate_cluster" {
  name = "${local.tenant}-cluster"
  setting {
    name  = "containerInsights"
    value = "enabled"
  }
}
*/
resource "aws_ecs_cluster" "bill_payment_fargate_cluster" {
  name = "${local.tenant}-bill-payment-cluster"
  setting {
    name  = "containerInsights"
    value = "enabled"
  }
}

resource "aws_ecr_repository" "fluent_bit_ecr" {
  image_tag_mutability = "IMMUTABLE"
  name                 = "custom-fluent-bit"
}

resource "aws_secretsmanager_secret" "datadog_key" {
  name = "${local.tenant}/datadog"
}

resource "aws_ecr_pull_through_cache_rule" "example" {
  ecr_repository_prefix = "ecr-public"
  upstream_registry_url = "public.ecr.aws"
}

resource "aws_s3_bucket" "mp_ios_lambda" {
  bucket = "${local.account_id}-mp-ios-lambda"

  # Prevent accidental deletion of this S3 bucket
  lifecycle {
    prevent_destroy = true
  }

  versioning {
    enabled = true
  }

  tags = {
    Environment = var.environment
  }
}

resource "aws_lambda_function" "mp_ios_subscription" {
  function_name = local.function_name
  role          = aws_iam_role.lambda_exec.arn

  filename      = "src/lambda.zip"
  
  handler       = "lambda_function.lambda_handler"
  runtime       = "python3.12"

  environment {
    variables = {
      SQS_QUEUE_URL = "https://sqs.${element(split(":", var.mp_ios_sqs_queue_arn), 3)}.amazonaws.com/${element(split(":", var.mp_ios_sqs_queue_arn), 4)}/${element(split(":", var.mp_ios_sqs_queue_arn), 5)}"
    }
  }
}

resource "aws_cloudwatch_log_group" "lambda" {
  name = "/aws/lambda/${local.function_name}"

  retention_in_days = 90

  lifecycle {
    create_before_destroy = true
    prevent_destroy       = false
  }
}

resource "aws_iam_role" "lambda_exec" {
  name = "mp_ios_lambda_execution_role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }

    ]
  })
}

resource "aws_iam_policy_attachment" "lambda_exec" {
  name       = "mp_ios_lambda_execution_role_policy"
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"  # Basic execution role
  roles      = [aws_iam_role.lambda_exec.name]
}

resource "aws_lambda_function_url" "mp_ios_subscription" {
  function_name      = aws_lambda_function.mp_ios_subscription.function_name
  authorization_type = "NONE"

  cors {
    allow_credentials = true
    allow_origins     = ["*"]
    allow_methods     = ["*"]
    allow_headers     = ["date", "keep-alive"]
    expose_headers    = ["keep-alive", "date"]
    max_age           = 0
  }
}  

resource "aws_iam_policy" "lambda_sqs_policy" {
  name        = "lambda_sqs_policy"
  description = "Policy of lambda"
  
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [

         {
             "Sid": "",
             "Effect": "Allow",
             "Action": [
                 "logs:PutLogEvents",
                 "logs:CreateLogStream",
                 "logs:CreateLogGroup"
             ],
             "Resource": "*"
         },
         {
             "Sid": "",
             "Effect": "Allow",
             "Action": [
                 "sqs:*"
             ],
             "Resource": "*"
      }
    ]  
  })
}

resource "aws_iam_role_policy_attachment" "lambda_sqs_policy_attachment" {
  policy_arn = aws_iam_policy.lambda_sqs_policy.arn
  role       = aws_iam_role.lambda_exec.name
}

resource "aws_lambda_function_event_invoke_config" "event_destination_mapping" {
  destination_config {
    on_failure {
      destination = var.mp_ios_sqs_queue_arn
    }

    on_success {
      destination = var.mp_ios_sqs_queue_arn
    }
  }
  function_name    = local.function_name
}

module "waf_backend" {
  source = "../../modules/waf_backend"
  bill_payment_lb_arn = module.bill-payment-service.alb_arn
  prefix = local.tenant
}