[
  {
    "name": "${container_name}",
    "image": "${app_image}",
    "cpu": ${cpu},
    "memory": ${memory},
    "essential": true,
    "mountPoints": [],
    "environment" : [
      {
        "name": "SPRING_PROFILES_ACTIVE",
        "value": "${app_environments}"
      },
      {
        "name": "DD_ENV",
        "value": "${environment}"
      },{
        "name": "DD_SERVICE",
        "value": "${service_name}"
      }
    ],
    "dockerLabels": {
      "com.datadoghq.tags.env": "${environment}",
      "com.datadoghq.tags.service": "${service_name}"
    },
    "secrets": [
      {
        "name": "SENDGRID_API_KEY",
        "valueFrom": "${secret_arns[1]}:SENDGRID_API_KEY::"
      },
      {
        "name": "GENIAL_ID",
        "valueFrom": "${secret_arns[1]}:GENIAL_ID::"
      },
      {
        "name": "GENIAL_SECRET",
        "valueFrom": "${secret_arns[1]}:GENIAL_SECRET::"
      },
      {
        "name": "MONGODB_CONNECT_URI",
        "valueFrom": "${secret_arns[1]}:MONGODB_CONNECT_URI::"
      },
      {
        "name": "KEYCLOAK_CREDENTIALS_SECRET",
        "valueFrom": "${secret_arns[1]}:KEYCLOAK_CREDENTIALS_SECRET::"
      },
      {
        "name": "KEYCLOAK_USERNAME",
        "valueFrom": "${secret_arns[1]}:KEYCLOAK_USERNAME::"
      },
      {
        "name": "KEYCLOAK_PASSWORD",
        "valueFrom": "${secret_arns[1]}:KEYCLOAK_PASSWORD::"
      },
      {
        "name": "KEYCLOAK_CLIENT_ID",
        "valueFrom": "${secret_arns[1]}:KEYCLOAK_CLIENT_ID::"
      },
      {
        "name": "NETSAC_USERNAME",
        "valueFrom": "${secret_arns[1]}:NETSAC_USERNAME::"
      },
      {
        "name": "NETSAC_PASSWORD",
        "valueFrom": "${secret_arns[1]}:NETSAC_PASSWORD::"
      },
      {
        "name": "NETSAC_SERVICE_ACCOUNT",
        "valueFrom": "${secret_arns[1]}:NETSAC_SERVICE_ACCOUNT::"
      },
      {
        "name": "GOOGLE_PLAY_STORE",
        "valueFrom": "${secret_arns[1]}:GOOGLE_PLAY_STORE::"
      },
      {
        "name": "GOOGLE_FIREBASE",
        "valueFrom": "${secret_arns[1]}:GOOGLE_FIREBASE::"
      },
      {
        "name": "ME_POUPE_API_CLIENT_ID",
        "valueFrom": "${secret_arns[1]}:ME_POUPE_API_CLIENT_ID::"
      },{
        "name": "ME_POUPE_API_CLIENT_SECRET",
        "valueFrom": "${secret_arns[1]}:ME_POUPE_API_CLIENT_SECRET::"
      }
    ],
    "portMappings": [
      {
        "containerPort": ${app_port},
        "hostPort": ${app_port}
      }
    ],
    "volumesFrom": [],
    "logConfiguration": {
      "logDriver":"awsfirelens",
      "options": {
        "Name": "cloudwatch",
        "region": "us-east-1",
        "log_group_name": "/ecs/${task_name}",
        "auto_create_group": "false",
        "log_stream_name": "ecs/${task_name}/$(ecs_task_id)"
      }
    }
  },
  {
    "name": "datadog-agent",
    "image": "${account_id}.dkr.ecr.us-east-1.amazonaws.com/ecr-public/datadog/agent:7.49.1",
    "cpu": 10,
    "memory": 256,
    "essential": true,
    "environment" : [
      {
        "name": "DD_SITE",
        "value": "datadoghq.com"
      },
      {
        "name": "ECS_FARGATE",
        "value": "true"
      }
    ],
    "secrets": [
      {
        "name": "DD_API_KEY",
        "valueFrom": "${secret_arns[0]}:DD_API_KEY::"
      }
    ],
    "portMappings": [
      {
        "containerPort": 8125,
        "hostPort": 8125
      }
    ],
    "logConfiguration": {
      "logDriver": "awslogs",
      "options": {
        "awslogs-group": "/ecs/${task_name}-dd",
        "awslogs-region": "us-east-1",
        "awslogs-stream-prefix": "ecs"
      }
    }
  },
  {
    "essential": true,
    "image": "${fluent_bit_repository_url}:2.3",
    "name": "log_router",
    "firelensConfiguration": {
      "type": "fluentbit",
      "options": {
        "config-file-type": "file",
        "config-file-value": "/general/general.conf"
      }
    },
    "secrets": [
      {
        "name": "DD_API_KEY",
        "valueFrom": "${secret_arns[0]}:DD_API_KEY::"
      }
    ],
    "environment" : [
      {
        "name": "SERVICE_CONTAINER_NAME",
        "value": "${container_name}"
      },
      {
        "name": "DD_ENV",
        "value": "${environment}"
      },
      {
        "name": "DD_SERVICE",
        "value": "${service_name}"
      },
      {
        "name": "LOG_GROUP_NAME",
        "value": "/logs/${task_name}"
      },
      {
        "name": "REGION",
        "value": "us-east-1"
      }
    ],
    "logConfiguration": {
      "logDriver": "awslogs",
      "options": {
        "awslogs-group": "/ecs/${task_name}-dd",
        "awslogs-region": "us-east-1",
        "awslogs-stream-prefix": "ecs"
      }
    },
    "memoryReservation": 50
  }
]