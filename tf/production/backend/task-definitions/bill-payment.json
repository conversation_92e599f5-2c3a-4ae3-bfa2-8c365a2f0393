[
  {
    "name": "${container_name}",
    "image": "${app_image}",
    "cpu": ${cpu},
    "memory": ${memory},
    "essential": true,
    "stopTimeout": 120,
    "mountPoints": [],
    "environment" : [
      {
        "name": "MICRONAUT_ENVIRONMENTS",
        "value": "${app_environments}"
      },
      {
        "name": "JAVA_OPTS",
        "value": "-Xmx4096m"
      },
      {
        "name": "DD_ENV",
        "value": "${environment}"
      },
      {
        "name": "DD_SERVICE",
        "value": "${service_name}"
      }
    ],
    "dockerLabels": {
      "com.datadoghq.tags.env": "${environment}",
      "com.datadoghq.tags.service": "${service_name}"
    },
    "secrets": [
  {
    "name": "COMMUNICATION_CENTRE_INTEGRATION_BLIP_AUTH",
    "valueFrom": "${secret_arns[1]}"
  },
  {
    "name": "INTEGRATIONS_BIGDATACORP_ACCESS_TOKEN",
    "valueFrom": "${secret_arns[2]}:ACCESS_TOKEN::"
  },
  {
    "name": "INTEGRATIONS_BIGDATACORP_LOGIN",
    "valueFrom": "${secret_arns[2]}:LOGIN::"
  },
  {
    "name": "INTEGRATIONS_BIGDATACORP_PASSWORD",
    "valueFrom": "${secret_arns[2]}:PASSWORD::"
  },
  {
    "name": "INTEGRATIONS_USERPILOT_TOKEN",
    "valueFrom": "${secret_arns[3]}:TOKEN::"
  },
  {
    "name": "ARBI_CALLBACK_SECRET",
    "valueFrom": "${secret_arns[4]}:ARBI_SECRET::"
  },
  {
    "name": "INTEGRATIONS_QUOD_USERNAME",
    "valueFrom": "${secret_arns[4]}:QUOD_USERNAME::"
  },
  {
    "name": "INTEGRATIONS_QUOD_PASSWORD",
    "valueFrom": "${secret_arns[4]}:QUOD_PASSWORD::"
  },
  {
    "name": "MODATTA_B2B_SECRET",
    "valueFrom": "${secret_arns[4]}:MODATTA_B2B_SECRET::"
  },
  {
    "name": "BILL_PAYMENT_SECRET",
    "valueFrom": "${secret_arns[4]}:JWT_SECRET::"
  },
  {
    "name": "GOOGLE_SECRET",
    "valueFrom": "${secret_arns[4]}:GOOGLE_SECRET::"
  },
  {
    "name": "APPLE_SECRET",
    "valueFrom": "${secret_arns[4]}:APPLE_SECRET::"
  },
  {
    "name": "INTEGRATIONS_SERPRO_USERNAME",
    "valueFrom": "${secret_arns[4]}:SERPRO_USERNAME::"
  },
  {
    "name": "INTEGRATIONS_SERPRO_PASSWORD",
    "valueFrom": "${secret_arns[4]}:SERPRO_PASSWORD::"
  },
  {
    "name": "CELCOIN_CALLBACK_SECRET",
    "valueFrom": "${secret_arns[4]}:CELCOIN_SECRET::"
  },
  {
    "name": "INTEGRATIONS_SETTLEMENT_USERNAME",
    "valueFrom": "${secret_arns[4]}:SETTLEMENT_SERVICE_CLIENT_IDENTITY::"
  },
  {
    "name": "INTEGRATIONS_SETTLEMENT_PASSWORD",
    "valueFrom": "${secret_arns[4]}:SETTLEMENT_SERVICE_CLIENT_SECRET::"
  },
  {
    "name": "ME_POUPE_API_CLIENT_ID",
    "valueFrom": "${secret_arns[4]}:ME_POUPE_API_CLIENT_ID::"
  },
  {
    "name": "ME_POUPE_API_CLIENT_SECRET",
    "valueFrom": "${secret_arns[4]}:ME_POUPE_API_CLIENT_SECRET::"
  },
  {
    "name": "INTEGRATIONS_FIREBASE_CLOUD_MESSAGING_JSON",
    "valueFrom": "${secret_arns[4]}:FIREBASE_CLOUD_MESSAGING_JSON::"
  },
  {
    "name": "INTEGRATIONS_CELCOIN_USERNAME",
    "valueFrom": "${secret_arns[5]}:USERNAME::"
  },
  {
    "name": "INTEGRATIONS_CELCOIN_PASSWORD",
    "valueFrom": "${secret_arns[5]}:PASSWORD::"
  },
  {
    "name": "INTEGRATIONS_CELCOIN_TOKEN_CLIENT_ID",
    "valueFrom": "${secret_arns[5]}:ID::"
  },
  {
    "name": "INTEGRATIONS_CELCOIN_TOKEN_CLIENT_SECRET",
    "valueFrom": "${secret_arns[5]}:SECRET::"
  },
  {
    "name": "MICRONAUT_HTTP_SERVICES_CELCOIN_SSL_KEY_STORE_PASSWORD",
    "valueFrom": "${secret_arns[5]}:MTLS_P12_PASSWORD::"
  },
  {
    "name": "INTEGRATIONS_ARBI_CLIENT_SECRET",
    "valueFrom": "${secret_arns[6]}:CLIENT_SECRET::"
  },
  {
    "name": "INTEGRATIONS_ARBI_USER_TOKEN",
    "valueFrom": "${secret_arns[6]}:USER_TOKEN::"
  },
  {
    "name": "INTEGRATIONS_ARBI_CLIENT_ID",
    "valueFrom": "${secret_arns[6]}:CLIENT_ID::"
  },
  {
    "name": "INTEGRATIONS_ARBI_ECM_CLIENT",
    "valueFrom": "${secret_arns[7]}:CLIENT::"
  },
  {
    "name": "INTEGRATIONS_ARBI_ECM_API_KEY",
    "valueFrom": "${secret_arns[7]}:API_KEY::"
  },
  {
    "name": "INTEGRATIONS_ARBI_ECM_USERNAME",
    "valueFrom": "${secret_arns[7]}:USERNAME::"
  },
  {
    "name": "INTEGRATIONS_ARBI_ECM_PASSWORD",
    "valueFrom": "${secret_arns[7]}:PASSWORD::"
  },
  {
    "name": "INTEGRATIONS_ARBI_FEPWEB_USERNAME",
    "valueFrom": "${secret_arns[7]}:FEPWEB_USERNAME::"
  },
  {
    "name": "INTEGRATIONS_ARBI_FEPWEB_PASSWORD",
    "valueFrom": "${secret_arns[7]}:FEPWEB_PASSWORD::"
  },
  {
    "name": "INTEGRATIONS_INTERCOM_TOKEN",
    "valueFrom": "${secret_arns[8]}:TOKEN::"
  },
  {
    "name": "INTEGRATIONS_INTERCOM_WEB_ID_VERIFICATION_SECRET",
    "valueFrom": "${secret_arns[8]}:WEB::"
  },
  {
    "name": "INTEGRATIONS_INTERCOM_IOS_ID_VERIFICATION_SECRET",
    "valueFrom": "${secret_arns[8]}:IOS::"
  },
  {
    "name": "INTEGRATIONS_INTERCOM_ANDROID_ID_VERIFICATION_SECRET",
    "valueFrom": "${secret_arns[8]}:ANDROID::"
  },
  {
    "name": "INTEGRATIONS_REVENUE_CAT_SECRET_KEY",
    "valueFrom": "${secret_arns[4]}:REVENUE_CAT_SECRET_KEY::"
  },
  {
    "name": "INTEGRATIONS_REVENUE_CAT_SECRET_KEY_V1",
    "valueFrom": "${secret_arns[4]}:REVENUE_CAT_SECRET_KEY_V1::"
  },
  {
    "name": "REVENUE_CAT_CALLBACK_SECRET",
    "valueFrom": "${secret_arns[4]}:REVENUE_CAT_CALLBACK_SECRET::"
  },
  {
    "name":  "INTEGRATIONS_INVESTMENT_MANAGER_CLIENT_ID",
    "valueFrom": "${secret_arns[4]}:INVESTMENT_MANAGER_CLIENT_ID::"
  },
  {
    "name":  "INTEGRATIONS_INVESTMENT_MANAGER_CLIENT_SECRET",
    "valueFrom": "${secret_arns[4]}:INVESTMENT_MANAGER_CLIENT_SECRET::"
  },
  {
    "name":  "INTEGRATIONS_FLUENCY_CALLBACK_IDENTITY",
    "valueFrom": "${secret_arns[4]}:FLUENCY_CALLBACK_IDENTITY::"
  },
  {
    "name":  "INTEGRATIONS_FLUENCY_CALLBACK_SECRET",
    "valueFrom": "${secret_arns[4]}:FLUENCY_CALLBACK_SECRET::"
  },
  {
    "name":  "INTEGRATIONS_VOOMP_CALLBACK_IDENTITY",
    "valueFrom": "${secret_arns[4]}:VOOMP_CALLBACK_IDENTITY::"
  },
  {
    "name":  "INTEGRATIONS_VOOMP_CALLBACK_SECRET",
    "valueFrom": "${secret_arns[4]}:VOOMP_CALLBACK_SECRET::"
  }
],
    "portMappings": [
      {
        "protocol": "tcp",
        "containerPort": 8443,
        "hostPort": 8443
      }
    ],
    "volumesFrom": [],
    "logConfiguration": {
      "logDriver":"awsfirelens",
      "options": {
        "Name": "cloudwatch",
        "region": "us-east-1",
        "log_group_name": "/ecs/${task_name}",
        "auto_create_group": "false",
        "log_stream_name": "ecs/${task_name}/$(ecs_task_id)"
      }
    }
  },
  {
    "name": "datadog-agent",
    "image": "${account_id}.dkr.ecr.us-east-1.amazonaws.com/ecr-public/datadog/agent:7.49.1",
    "cpu": 51,
    "memory": 256,
    "essential": true,
    "environment" : [
      {
        "name": "DD_SITE",
        "value": "datadoghq.com"
      },
      {
        "name": "ECS_FARGATE",
        "value": "true"
      }
    ],
    "secrets": [
      {
        "name": "DD_API_KEY",
        "valueFrom": "${secret_arns[0]}:DD_API_KEY::"
      }
    ],
    "portMappings": [
      {
        "containerPort": 8125,
        "hostPort": 8125
      }
    ],
    "logConfiguration": {
      "logDriver": "awslogs",
      "options": {
        "awslogs-group": "/ecs/${task_name}-dd",
        "awslogs-region": "us-east-1",
        "awslogs-stream-prefix": "ecs"
      }
    }
  },
  {
    "essential": true,
    "image": "${fluent_bit_repository_url}:2.3",
    "name": "log_router",
    "firelensConfiguration": {
      "type": "fluentbit",
      "options": {
        "config-file-type": "file",
        "config-file-value": "/general/general.conf"
      }
    },
    "secrets": [
      {
        "name": "DD_API_KEY",
        "valueFrom": "${secret_arns[0]}:DD_API_KEY::"
      }
    ],
    "environment" : [
      {
        "name": "SERVICE_CONTAINER_NAME",
        "value": "${container_name}"
      },
      {
        "name": "DD_ENV",
        "value": "${environment}"
      },
      {
        "name": "DD_SERVICE",
        "value": "${service_name}"
      },
      {
        "name": "LOG_GROUP_NAME",
        "value": "/logs/${task_name}"
      },
      {
        "name": "REGION",
        "value": "us-east-1"
      }
    ],
    "logConfiguration": {
      "logDriver": "awslogs",
      "options": {
        "awslogs-group": "/ecs/${task_name}-dd",
        "awslogs-region": "us-east-1",
        "awslogs-stream-prefix": "ecs"
      }
    },
    "memoryReservation": 50
  }
]