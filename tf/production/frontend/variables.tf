variable "aws_region" {
  description = "The AWS region things are created in"
  default     = "us-east-1"
}

variable "environment" {
  description = "The environment"
  default     = "production"
}

variable "account_id" {
  description = "The AWS account id"
  default     = "************"
}

variable "certificate_arn" {
  description = "The arn for *.friday.ai certificate"
  default = "arn:aws:acm:us-east-1:************:certificate/15cb65a9-1643-45d3-8a25-38d5a6218ff7"
}

variable "certificate_arn_mepoupe" {
  description = "The arn for *.mepoupe.com certificate"
  default = "arn:aws:acm:us-east-1:************:certificate/3e9d0cf8-73d5-4e47-abf2-bb47e92dd85a"
}

variable "certificate_arn_mepoupe_app" {
  description = "The arn for *.mepoupe.app certificate"
  default = "arn:aws:acm:us-east-1:************:certificate/16616f3f-a67d-4484-9d4a-26452bc51801"
}
