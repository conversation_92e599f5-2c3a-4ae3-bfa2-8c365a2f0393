[{"name": "${container_name}", "image": "${app_image}", "cpu": 0, "essential": true, "environment": [{"name": "MICRONAUT_ENVIRONMENTS", "value": "${app_environments}"}, {"name": "JAVA_OPTS", "value": "-Xmx2048m"}, {"name": "DD_ENV", "value": "${environment}"}, {"name": "DD_SERVICE", "value": "${service_name}"}], "dockerLabels": {"com.datadoghq.tags.env": "${environment}", "com.datadoghq.tags.service": "${service_name}"}, "portMappings": [{"containerPort": 8443, "hostPort": 8443, "protocol": "tcp"}], "logConfiguration": {"logDriver": "awsfire<PERSON>s", "options": {"Name": "cloudwatch", "region": "sa-east-1", "log_group_name": "/ecs/${task_name}", "auto_create_group": "false", "log_stream_name": "ecs/${task_name}/$(ecs_task_id)"}}, "volumesFrom": [], "mountPoints": [], "systemControls": []}, {"name": "${container_name}Ocr", "image": "${account_id}.dkr.ecr.sa-east-1.amazonaws.com/connect-utility-ocr:${app_version_ocr}", "cpu": 0, "essential": true, "environment": [{"name": "PORT", "value": "8037"}], "dockerLabels": {"com.datadoghq.tags.env": "${environment}", "com.datadoghq.tags.service": "${service_name}"}, "portMappings": [{"containerPort": 8037, "hostPort": 8037, "protocol": "tcp"}], "logConfiguration": {"logDriver": "awsfire<PERSON>s", "options": {"Name": "cloudwatch", "region": "sa-east-1", "log_group_name": "/ecs/${task_name}", "auto_create_group": "false", "log_stream_name": "ecs/${task_name}/$(ecs_task_id)"}}, "volumesFrom": [], "mountPoints": [], "systemControls": []}, {"name": "${container_name}Speech2Text", "image": "${account_id}.dkr.ecr.sa-east-1.amazonaws.com/connect-utility-s2t:${app_version_s2t}", "cpu": 0, "essential": true, "environment": [{"name": "PORT", "value": "8029"}], "dockerLabels": {"com.datadoghq.tags.env": "${environment}", "com.datadoghq.tags.service": "${service_name}"}, "portMappings": [{"containerPort": 8029, "hostPort": 8029, "protocol": "tcp"}], "logConfiguration": {"logDriver": "awsfire<PERSON>s", "options": {"Name": "cloudwatch", "region": "sa-east-1", "log_group_name": "/ecs/${task_name}", "auto_create_group": "false", "log_stream_name": "ecs/${task_name}/$(ecs_task_id)"}}, "volumesFrom": [], "mountPoints": [], "systemControls": []}, {"name": "datadog-agent", "image": "${account_id}.dkr.ecr.sa-east-1.amazonaws.com/ecr-public/datadog/agent:7.49.1", "cpu": 0, "essential": true, "environment": [{"name": "DD_SITE", "value": "datadoghq.com"}, {"name": "ECS_FARGATE", "value": "true"}], "secrets": [{"name": "DD_API_KEY", "valueFrom": "${secret_arns[0]}:DD_API_KEY::"}], "portMappings": [{"containerPort": 8125, "hostPort": 8125}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/${task_name}-dd", "awslogs-region": "sa-east-1", "awslogs-stream-prefix": "ecs"}}}, {"image": "${fluent_bit_repository_url}:2.3", "name": "log_router", "essential": true, "cpu": 0, "firelensConfiguration": {"type": "fluentbit", "options": {"config-file-type": "file", "config-file-value": "/general/general.conf"}}, "secrets": [{"name": "DD_API_KEY", "valueFrom": "${secret_arns[0]}:DD_API_KEY::"}], "environment": [{"name": "SERVICE_CONTAINER_NAME", "value": "${container_name}"}, {"name": "DD_ENV", "value": "${environment}"}, {"name": "DD_SERVICE", "value": "${service_name}"}, {"name": "LOG_GROUP_NAME", "value": "/logs/${task_name}"}, {"name": "REGION", "value": "sa-east-1"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/${task_name}-dd", "awslogs-region": "sa-east-1", "awslogs-stream-prefix": "ecs"}}, "memoryReservation": 50}]