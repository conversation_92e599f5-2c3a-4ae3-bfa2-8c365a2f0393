locals {
  account_id                             = "************"
  aws_region                             = "sa-east-1"
  production_availability_zones          = ["sa-east-1a", "sa-east-1b"]
  ecs_sqs_list_policy_resource           = ["arn:aws:sqs:us-east-1:${local.account_id}:*"]
  errors_bucket_name                     = "mepoupe-connect-utility-errors"
  connect_utility_ocr_repository_name    = "connect-utility-ocr"
  connect_utility_s2t_repository_name    = "connect-utility-s2t"
  tenant                                 = "me-poupe"
  first_run                              = false
  cluster_name                           = "${local.tenant}-connect-utility-cluster"
  container_name                         = "ConnectUtility"
  service_name                           = "${local.tenant}-connect-utility"
  task_name                              = "${local.tenant}-connect-utility-task"
}

data "terraform_remote_state" "us-east" {
  backend = "s3"
  config  = {
    bucket = "me-poupe-backend-infrastructure-production"
    key    = "backend/terraform.tfstate"
    region = "us-east-1"
  }
}

data "aws_kms_key" "connect_utility_replica_key" {
  key_id = "alias/utility-connect-key"
}

resource "aws_secretsmanager_secret" "datadog_key" {
  name = "${local.tenant}/datadog"
}

provider "aws" {
  region              = local.aws_region
  allowed_account_ids = [local.account_id]
}

module "backend" {
  source              = "../../modules/backend"
  environment         = var.environment
  backend_bucket_name = "sa-************-backend-infrastructure"
  dynamodb_table_name = "backend-infrastructure-lock-table"
}

terraform {
  backend "s3" {
    bucket         = "sa-************-backend-infrastructure"
    key            = "prod/terraform.tfstate"
    dynamodb_table = "backend-infrastructure-lock-table"
    encrypt        = true
    region         = "sa-east-1"
  }
}

module "vpc" {
  source  = "terraform-aws-modules/vpc/aws"
  version = "5.1.2"
  name    = "mepoupe-vpc"
  cidr    = "10.0.0.0/16"

  azs             = ["sa-east-1a", "sa-east-1b"]
  public_subnets  = ["10.0.1.0/24", "10.0.2.0/24"]
  private_subnets = ["10.0.101.0/24", "10.0.102.0/24"]

  enable_nat_gateway = var.nat_gateway_enabled
  single_nat_gateway = true
  enable_vpn_gateway = false

  tags = {
    Terraform   = "true"
    Environment = var.environment
  }
}

resource "aws_ecr_repository" "connect_utility_ocr_repository" {
  image_tag_mutability = "IMMUTABLE"
  name                 = local.connect_utility_ocr_repository_name
}

resource "aws_ecr_repository" "connect_utility_s2t_repository" {
  image_tag_mutability = "IMMUTABLE"
  name                 = local.connect_utility_s2t_repository_name
}

module "connect_utility_ocr_image_version" {
  count          = local.first_run ? 0 : 1
  source         = "../../modules/image_version"
  container_name = "${local.container_name}Ocr"
  service_name   = "${local.service_name}-service"
  cluster_name   = local.cluster_name
  task_name      = local.task_name
}

module "connect_utility_s2t_image_version" {
  count          = local.first_run ? 0 : 1
  source         = "../../modules/image_version"
  container_name = "${local.container_name}Speech2Text"
  service_name   = "${local.service_name}-service"
  cluster_name   = local.cluster_name
  task_name      = local.task_name
}

locals {
  image_tag_ocr = local.first_run ? "FIRST_RUN" : module.connect_utility_ocr_image_version[0].image_tag
  image_tag_s2t = local.first_run ? "FIRST_RUN" : module.connect_utility_s2t_image_version[0].image_tag
}

resource "aws_ecs_cluster" "mepoupe_fargate_cluster" {
  name = local.cluster_name
  setting {
    name  = "containerInsights"
    value = "enabled"
  }
}

resource "aws_ecr_repository" "fluent_bit_ecr" {
  image_tag_mutability = "IMMUTABLE"
  name                 = "custom-fluent-bit"
}

resource "aws_s3_bucket" "connect-utility-errors-bucket" {
  bucket = local.errors_bucket_name

  lifecycle {
    prevent_destroy = true
  }

  tags = {
    Environment = var.environment
  }
}

resource "aws_dynamodb_table" "connect_utility_table" {
  name         = "ConnectUtility"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "PartitionKey"
  range_key    = "ScanKey"

  attribute {
    name = "PartitionKey"
    type = "S"
  }

  attribute {
    name = "ScanKey"
    type = "S"
  }

  tags = {
    Environment = var.environment
    Project     = "ConnectUtility"
  }
}

module "friday_connect_utility_task" {
  source = "../../modules/fargate_task"

  prefix                     = local.service_name
  service_name               = "${local.service_name}-service"
  ecr_repository_name        = "connect-utility"
  fargate_cpu                = 2048
  fargate_memory             = 4096
  ephemeral_storage          = 100
  task_definition            = "${path.module}/task-definitions/connect-utility.json"
  dynamo_access_enabled      = true
  ecs_dynamo_policy_resource = [
    aws_dynamodb_table.connect_utility_table.arn,
    "${aws_dynamodb_table.connect_utility_table.arn}/*",
  ]
  s3_read_objects  = true
  textract_enabled = true
  s3_bucket_arns   = [
    aws_s3_bucket.connect-utility-errors-bucket.arn,
    data.terraform_remote_state.us-east.outputs.incoming_emails_s3_arn
  ]
  sqs_access_enabled      = true
  ecs_sqs_policy_resource = local.ecs_sqs_list_policy_resource
  sns_access_enabled      = true
  ecs_sns_policy_resource = [
    data.terraform_remote_state.us-east.outputs.incoming_emails_sns_arn
  ]
  user_pool_arn_enabled     = false
  kms_enabled               = true
  kms_key_arns              = [data.aws_kms_key.connect_utility_replica_key.arn]
  fluent_bit_repository_url = aws_ecr_repository.fluent_bit_ecr.repository_url
  aws_region                = local.aws_region
  secrets_enabled = true
  secrets_arns = [aws_secretsmanager_secret.datadog_key.arn]
  cluster_name = local.cluster_name
  container_name = local.container_name
  account_id = local.account_id
  first_run = local.first_run
  tags = {
    Environment = var.environment
  }

  environment = var.environment
  app_environments = local.tenant
  app_port = 8443

  other_container_definitions = {
    app_version_ocr = local.image_tag_ocr
    app_version_s2t = local.image_tag_s2t
  }
}

module "utility_account_dlq" {
  source  = "terraform-aws-modules/sqs/aws"
  version = "~> 2.0"
  name    = "utility_account_dlq"
}

module "connect_utility_service" {
  source = "../../modules/fargate_service"

  aws_ecs_cluster         = aws_ecs_cluster.mepoupe_fargate_cluster
  aws_private_subnet_id   = module.vpc.private_subnets
  aws_public_subnet_id    = module.vpc.public_subnets
  aws_vpc_id              = module.vpc.vpc_id
  prefix                  = local.service_name
  container_name          = "ConnectUtility"
  app_port                = 8443
  app_count               = 1
  health_check_path       = "/health"
  task_definition         = module.friday_connect_utility_task
  certificate_arn_enabled = false
  load_balance_enabled = false
  app_protocol = "HTTPS"
}

data "aws_iam_policy_document" "docker_runner_policy_document" {
  statement {
    actions = [
      "iam:PassRole",
      "ecs:UpdateService",
      "ecs:RegisterTaskDefinition",
      "ecs:DescribeTasks",
      "ecs:DescribeTaskDefinition",
      "ecs:StartTask",
      "ecr:UploadLayerPart",
      "ecr:PutImage",
      "ecr:ListImages",
      "ecr:InitiateLayerUpload",
      "ecr:GetDownloadUrlForLayer",
      "ecr:GetAuthorizationToken",
      "ecr:DescribeRepositories",
      "ecr:DescribeImages",
      "ecr:CompleteLayerUpload",
      "ecr:BatchGetImage",
      "ecr:BatchCheckLayerAvailability",
      "s3:GetObject",
      "s3:GetObjectTagging",
      "s3:PutObject",
      "s3:PutObjectTagging",
      "s3:ListBucket"
    ]
    resources = ["*"]
  }
}

resource "aws_iam_policy" "docker_runner_policy" {
  name   = "DockerMachinePolicy"
  path   = "/"
  policy = data.aws_iam_policy_document.docker_runner_policy_document.json
}

resource "aws_ecr_pull_through_cache_rule" "public_ecr" {
  ecr_repository_prefix = "ecr-public"
  upstream_registry_url = "public.ecr.aws"
}
