# backend-infrastructure

Infrastructure as code for the Friday MePoupe! app's backend.

Erros conhecidos ao criar o ambiente e suas soluções: 

│ Error: creating CloudFront Distribution: InvalidLambdaFunctionAssociation: Lambda@Edge cannot retrieve the specified Lambda function. Update the IAM policy to add permission: lambda:GetFunction for resource: arn:aws:lambda:us-east-1:677122980969:function:static-lambda-proxy-origin-request:1 and try again.
- Necessario acessar o painel da amazon e fazer o Publish de uma nova versão de ambos os lambdas

│ Error: creating CloudFront Distribution: InvalidViewerCertificate: The specified SSL certificate doesn't exist, isn't in us-east-1 region, isn't valid, or doesn't include a valid certificate chain.
│       status code: 400, request id: 9637cad4-6645-460a-a3ca-5401b90e0f20
- Criar o certificado diretamente na amazon (https://us-east-1.console.aws.amazon.com/acm/home?region=us-east-1#/certificates/request)
- O certificado deve conter 2 DNS (dominio.com e *.dominio.com)
- Pedir para alguem adicionar (Enzo) o CNAME name e value no Route53 da conta onde ficam os domínios

│ Error: Failed to get existing workspaces: S3 bucket "me-poupe-backend-infrastructure-production" does not exist.
- criar o bucket manualmente na amazon.

│ Error: Error acquiring the state lock
│
│ Error message: 2 errors occurred:
│       * operation error DynamoDB: PutItem, https response error StatusCode: 400, RequestID: DO40MLUPP7JHHNHJHBCL6UIOA3VV4KQNSO5AEMVJF66Q9ASUAAJG, ResourceNotFoundException: Requested resource not found
│       * Unable to retrieve item from DynamoDB table "backend-infrastructure-lock-table": operation error DynamoDB: GetItem, https response error StatusCode: 400, RequestID: 7SS3H22L9ONKMOVR89IUPVKH9JVV4KQNSO5AEMVJF66Q9ASUAAJG, ResourceNotFoundException: Requested resource
- comentar o bloco: terraform {backend "s3"{...}}
- será necessario fazer um terraform init -migrate-state
- depois poderá rodar o terraform apply normalmente

│ Error: reading ECS Task Definition (me-poupe-app-task): ClientException: Unable to describe task definition.
- O serviço não existe ainda. Tem que comentar o bloco: module "fargate_task_image_version" {...}
- na propriedade app_image pouco abaixo, substitua a tag que deverá estar "${module.fargate_task_image_version.image_tag}" por qq string nao vazia. "void", por exemplo.